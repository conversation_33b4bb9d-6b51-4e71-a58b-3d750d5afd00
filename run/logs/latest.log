[09:10:32] [Client Thread/INFO]: Setting user: <PERSON><PERSON><PERSON>B<PERSON>
[09:10:32] [Client Thread/INFO]: (Session ID is token:0:<PERSON><PERSON><PERSON><PERSON><PERSON>)
[09:10:33] [Client Thread/INFO]: LWJGL Version: 2.9.3
[09:10:34] [Client Thread/INFO]: [OptiFine] OptiFine 1.8.9 HD U M6 Pre-2
[09:10:34] [Client Thread/INFO]: [OptiFine] Build: OptiFine 1.8.9 HD U M6 Pre-2
[09:10:34] [Client Thread/INFO]: [OptiFine] OS: Windows 11 (amd64) version 10.0
[09:10:34] [Client Thread/INFO]: [OptiFine] Java: 21.0.6, Oracle Corporation
[09:10:34] [Client Thread/INFO]: [OptiFine] VM: Java HotSpot(TM) 64-Bit Server VM (mixed mode, sharing), Oracle Corporation
[09:10:34] [Client Thread/INFO]: [OptiFine] LWJGL: 2.9.3
[09:10:34] [Client Thread/INFO]: [OptiFine] OpenGL: AMD Radeon(TM) R5 240, version 4.6.14830 Compatibility Profile Context 22.6.1 27.20.20913.2000, ATI Technologies Inc.
[09:10:34] [Client Thread/INFO]: [OptiFine] OpenGL Version: 4.6.14830
[09:10:34] [Client Thread/INFO]: [OptiFine] OpenGL Fancy fog: Not available (GL_NV_fog_distance)
[09:10:34] [Client Thread/INFO]: [OptiFine] Maximum texture size: 16384x16384
[09:10:34] [Client Thread/INFO]: [Shaders] OpenGL Version: 4.6.14830 Compatibility Profile Context 22.6.1 27.20.20913.2000
[09:10:34] [Client Thread/INFO]: [Shaders] Vendor:  ATI Technologies Inc.
[09:10:34] [Client Thread/INFO]: [Shaders] Renderer: AMD Radeon(TM) R5 240
[09:10:34] [Client Thread/INFO]: [Shaders] Capabilities:  2.0  2.1  3.0  3.2  4.0 
[09:10:34] [Client Thread/INFO]: [Shaders] GL_MAX_DRAW_BUFFERS: 8
[09:10:34] [Client Thread/INFO]: [Shaders] GL_MAX_COLOR_ATTACHMENTS_EXT: 8
[09:10:34] [Client Thread/INFO]: [Shaders] GL_MAX_TEXTURE_IMAGE_UNITS: 32
[09:10:34] [Client Thread/INFO]: [Shaders] Load shaders configuration.
[09:10:34] [Client Thread/INFO]: [Shaders] Save shaders configuration.
[09:10:34] [Client Thread/INFO]: [Shaders] No shaderpack loaded.
[09:10:34] [Client Thread/INFO]: Reloading ResourceManager: Default
[09:10:34] [Client Thread/INFO]: [OptiFine] *** Reloading textures ***
[09:10:34] [Client Thread/INFO]: [OptiFine] Resource packs: Default
[09:10:35] [Sound Library Loader/INFO]: Starting up SoundSystem...
[09:10:35] [Thread-1/INFO]: Initializing LWJGL OpenAL
[09:10:35] [Thread-1/INFO]: (The LWJGL binding of OpenAL.  For more information, see http://www.lwjgl.org)
[09:10:35] [Thread-1/INFO]: OpenAL initialized.
[09:10:35] [Sound Library Loader/INFO]: Sound engine started
[09:10:39] [Client Thread/INFO]: [OptiFine] Mipmap levels: 4
[09:10:39] [Client Thread/INFO]: [OptiFine] Multitexture: false
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/0_glass_white/glass_pane_white.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/0_glass_white/glass_white.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/10_glass_purple/glass_pane_purple.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/10_glass_purple/glass_purple.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/11_glass_blue/glass_blue.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/11_glass_blue/glass_pane_blue.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/12_glass_brown/glass_brown.properties
[09:10:39] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/12_glass_brown/glass_pane_brown.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/13_glass_green/glass_green.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/13_glass_green/glass_pane_green.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/14_glass_red/glass_pane_red.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/14_glass_red/glass_red.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/15_glass_black/glass_black.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/15_glass_black/glass_pane_black.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/1_glass_orange/glass_orange.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/1_glass_orange/glass_pane_orange.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/2_glass_magenta/glass_magenta.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/2_glass_magenta/glass_pane_magenta.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/3_glass_light_blue/glass_light_blue.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/3_glass_light_blue/glass_pane_light_blue.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/4_glass_yellow/glass_pane_yellow.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/4_glass_yellow/glass_yellow.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/5_glass_lime/glass_lime.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/5_glass_lime/glass_pane_lime.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/6_glass_pink/glass_pane_pink.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/6_glass_pink/glass_pink.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/7_glass_gray/glass_gray.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/7_glass_gray/glass_pane_gray.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/8_glass_silver/glass_pane_silver.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/8_glass_silver/glass_silver.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/9_glass_cyan/glass_cyan.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/9_glass_cyan/glass_pane_cyan.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/bookshelf.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/glass.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/glasspane.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] ConnectedTextures: mcpatcher/ctm/default/sandstone.properties
[09:10:40] [Client Thread/INFO]: [OptiFine] Multipass connected textures: false
[09:10:40] [Client Thread/INFO]: [OptiFine] BetterGrass: Parsing default configuration optifine/bettergrass.properties
[09:10:42] [Client Thread/INFO]: Created: 1024x512 textures-atlas
[09:10:42] [Client Thread/INFO]: [OptiFine] Animated sprites: 11
[09:10:43] [Client Thread/INFO]: [OptiFine] *** Reloading custom textures ***
[09:10:43] [Client Thread/INFO]: [OptiFine] Enable face culling: acacia_leaves, birch_leaves, dark_oak_leaves, jungle_leaves, oak_leaves, spruce_leaves
[09:10:52] [Client Thread/ERROR]: Can't ping localhost: Internal Exception: io.netty.handler.codec.DecoderException: java.lang.UnsupportedOperationException: direct buffer
[09:10:56] [Client Thread/ERROR]: Can't ping localhost: Internal Exception: io.netty.handler.codec.DecoderException: java.lang.UnsupportedOperationException: direct buffer
